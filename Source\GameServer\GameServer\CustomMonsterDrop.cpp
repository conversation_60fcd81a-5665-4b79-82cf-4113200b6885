#include "CustomMonsterDrop.h"
#include <fstream>
#include <sstream>
#include <algorithm>
#include <random>
#include <iostream>
#include <ctime>

// Global random engine for drop calculations
static std::random_device rd;
static std::mt19937 gen(rd());

CustomMonsterDrop::CustomMonsterDrop()
{
	m_initialized = false;
	m_configPath = "";
	srand(static_cast<unsigned int>(time(nullptr)));
}

CustomMonsterDrop::~CustomMonsterDrop()
{
	Clear();
}

bool CustomMonsterDrop::Initialize(const std::string& configPath)
{
	m_configPath = configPath;
	Clear();
	
	LogInfo("CustomMonsterDrop initialized with config path: " + configPath);
	m_initialized = true;
	return true;
}

void CustomMonsterDrop::Clear()
{
	m_monsterInfos.clear();
	m_basicConfigs.clear();
	m_levelConfigs.clear();
	m_itemConfigs.clear();
	m_initialized = false;
}

bool CustomMonsterDrop::LoadMonsterDropConfig(int monsterId)
{
	std::string filename = m_configPath + "/Monster_" + std::to_string(monsterId) + ".txt";
	return LoadMonsterDropFile(filename, monsterId);
}

bool CustomMonsterDrop::LoadMonsterDropFile(const std::string& filename, int monsterId)
{
	std::ifstream file(filename);
	if (!file.is_open())
	{
		LogError("Failed to open monster drop file: " + filename);
		return false;
	}
	
	std::string line;
	int currentSection = -1;
	MonsterDropInfo info;
	MonsterDropBasic basicConfig;
	std::vector<MonsterDropLevel> levelConfigs;
	std::vector<MonsterDropItem> itemConfigs;
	
	info.monsterId = monsterId;
	
	while (std::getline(file, line))
	{
		line = TrimString(line);
		
		// Skip empty lines and comments
		if (IsEmptyLine(line) || IsCommentLine(line))
			continue;
			
		// Check for section markers
		if (line == "0")
		{
			currentSection = 0;
			continue;
		}
		else if (line == "1")
		{
			currentSection = 1;
			continue;
		}
		else if (line == "2")
		{
			currentSection = 2;
			continue;
		}
		else if (line == "3")
		{
			currentSection = 3;
			continue;
		}
		else if (line == "end")
		{
			currentSection = -1;
			continue;
		}
		
		// Parse section data
		switch (currentSection)
		{
		case 0:
			ParseSection0(line, info);
			break;
		case 1:
			ParseSection1(line, basicConfig);
			break;
		case 2:
			{
				MonsterDropLevel levelConfig;
				if (ParseSection2(line, levelConfig))
				{
					levelConfigs.push_back(levelConfig);
				}
			}
			break;
		case 3:
			{
				MonsterDropItem itemConfig;
				if (ParseSection3(line, itemConfig))
				{
					itemConfigs.push_back(itemConfig);
				}
			}
			break;
		}
	}
	
	file.close();
	
	// Store configurations
	m_monsterInfos[monsterId] = info;
	m_basicConfigs[monsterId] = basicConfig;
	m_levelConfigs[monsterId] = levelConfigs;
	m_itemConfigs[monsterId] = itemConfigs;
	
	LogInfo("Loaded monster drop config for ID: " + std::to_string(monsterId));
	return true;
}

bool CustomMonsterDrop::ParseSection0(const std::string& line, MonsterDropInfo& info)
{
	// Remove quotes and set description
	std::string desc = line;
	if (desc.front() == '"' && desc.back() == '"')
	{
		desc = desc.substr(1, desc.length() - 2);
	}
	info.description = desc;
	return true;
}

bool CustomMonsterDrop::ParseSection1(const std::string& line, MonsterDropBasic& config)
{
	std::vector<std::string> tokens = SplitString(line, '\t');
	if (tokens.size() >= 8)
	{
		config.mapNumber = std::stoi(tokens[0]);
		config.dropEnabled = std::stoi(tokens[1]);
		config.useGuaranteedDrop = std::stoi(tokens[2]);
		config.guaranteedDropCount = std::stoi(tokens[3]);
		config.levelRange = std::stoi(tokens[4]);
		config.reserved1 = std::stoi(tokens[5]);
		config.reserved2 = std::stoi(tokens[6]);
		config.reserved3 = std::stoi(tokens[7]);
		return true;
	}
	return false;
}

bool CustomMonsterDrop::ParseSection2(const std::string& line, MonsterDropLevel& config)
{
	std::vector<std::string> tokens = SplitString(line, '\t');
	if (tokens.size() >= 11)
	{
		config.useLevel = std::stoi(tokens[0]);
		config.weight = std::stoi(tokens[1]);
		config.additionalOption = std::stoi(tokens[2]);
		config.excellentRate = std::stoi(tokens[3]);
		config.excellentType = std::stoi(tokens[4]);
		config.setOption = std::stoi(tokens[5]);
		config.socketOption = std::stoi(tokens[6]);
		config.minLevel = std::stoi(tokens[7]);
		config.maxLevel = std::stoi(tokens[8]);
		if (tokens.size() > 9) config.reserved1 = std::stoi(tokens[9]);
		if (tokens.size() > 10) config.reserved2 = std::stoi(tokens[10]);
		return true;
	}
	return false;
}

bool CustomMonsterDrop::ParseSection3(const std::string& line, MonsterDropItem& config)
{
	std::vector<std::string> tokens = SplitString(line, '\t');
	if (tokens.size() >= 16)
	{
		config.level = std::stoi(tokens[0]);
		config.itemType1 = std::stoi(tokens[1]);
		config.itemType2 = std::stoi(tokens[2]);
		config.minLevel = std::stoi(tokens[3]);
		config.maxLevel = std::stoi(tokens[4]);
		config.luckyRate = std::stoi(tokens[5]);
		config.skillRate = std::stoi(tokens[6]);
		config.addOption = std::stoi(tokens[7]);
		config.durability = std::stoi(tokens[8]);
		config.excellentOption = std::stoi(tokens[9]);
		config.setItemValue = std::stoi(tokens[10]);
		config.socketCount = std::stoi(tokens[11]);
		config.useTime = std::stoi(tokens[12]);
		config.accountRestriction = std::stoi(tokens[13]);
		config.reserved = std::stoi(tokens[14]);
		config.weight = std::stoi(tokens[15]);
		return true;
	}
	return false;
}

std::vector<MonsterDropItem> CustomMonsterDrop::GenerateDropItems(int monsterId, int playerLevel, 
	int playerVipLevel, int playerAccountType)
{
	std::vector<MonsterDropItem> droppedItems;
	
	// Check if monster is configured
	if (!IsMonsterConfigured(monsterId))
	{
		LogError("Monster " + std::to_string(monsterId) + " is not configured");
		return droppedItems;
	}
	
	// Get configurations
	MonsterDropBasic* basicConfig = GetBasicConfig(monsterId);
	std::vector<MonsterDropItem>* itemConfigs = GetItemConfigs(monsterId);
	
	if (!basicConfig || !itemConfigs || basicConfig->dropEnabled == 0)
	{
		return droppedItems;
	}
	
	// Filter items based on player conditions
	std::vector<MonsterDropItem> eligibleItems;
	for (const auto& item : *itemConfigs)
	{
		if (CheckDropConditions(item, playerLevel, playerVipLevel, playerAccountType))
		{
			eligibleItems.push_back(item);
		}
	}
	
	if (eligibleItems.empty())
	{
		return droppedItems;
	}
	
	// Generate guaranteed drops
	if (basicConfig->useGuaranteedDrop > 0)
	{
		for (int i = 0; i < basicConfig->guaranteedDropCount; i++)
		{
			MonsterDropItem* selectedItem = SelectRandomItem(eligibleItems);
			if (selectedItem)
			{
				MonsterDropItem dropItem = *selectedItem;
				ApplyItemAttributes(dropItem);
				droppedItems.push_back(dropItem);
			}
		}
	}
	else
	{
		// Random drop logic
		MonsterDropItem* selectedItem = SelectRandomItem(eligibleItems);
		if (selectedItem)
		{
			MonsterDropItem dropItem = *selectedItem;
			ApplyItemAttributes(dropItem);
			droppedItems.push_back(dropItem);
		}
	}
	
	return droppedItems;
}

MonsterDropItem* CustomMonsterDrop::SelectRandomItem(const std::vector<MonsterDropItem>& items)
{
	if (items.empty())
		return nullptr;
		
	// Calculate total weight
	int totalWeight = GetTotalWeight(items);
	if (totalWeight <= 0)
		return nullptr;
		
	// Generate random number
	int randomValue = GetRandomNumber(1, totalWeight);
	
	// Find selected item based on weight
	int currentWeight = 0;
	for (size_t i = 0; i < items.size(); i++)
	{
		currentWeight += items[i].weight;
		if (randomValue <= currentWeight)
		{
			return const_cast<MonsterDropItem*>(&items[i]);
		}
	}
	
	return nullptr;
}

bool CustomMonsterDrop::CheckDropConditions(const MonsterDropItem& item, int playerLevel, 
	int playerVipLevel, int playerAccountType)
{
	// Check level restrictions
	if (item.level != -1)
	{
		// Find matching level config
		std::vector<MonsterDropLevel>* levelConfigs = GetLevelConfigs(item.level); // This should use monster ID
		if (levelConfigs)
		{
			bool levelMatched = false;
			for (const auto& levelConfig : *levelConfigs)
			{
				if (playerLevel >= levelConfig.minLevel && playerLevel <= levelConfig.maxLevel)
				{
					levelMatched = true;
					break;
				}
			}
			if (!levelMatched)
				return false;
		}
	}
	
	// Check VIP/account restrictions
	if (item.accountRestriction > 0)
	{
		if (playerVipLevel < item.accountRestriction)
			return false;
	}
	
	return true;
}

void CustomMonsterDrop::ApplyItemAttributes(MonsterDropItem& item)
{
	// Apply random level within range
	if (item.maxLevel > item.minLevel)
	{
		int level = GetRandomNumber(item.minLevel, item.maxLevel);
		// Set item level (this would depend on your item system)
	}
	
	// Apply lucky attribute
	if (GenerateLuckyAttribute(item.luckyRate))
	{
		// Set lucky attribute
	}
	
	// Apply skill attribute  
	if (GenerateSkillAttribute(item.skillRate))
	{
		// Set skill attribute
	}
	
	// Apply additional options
	if (item.addOption > 0)
	{
		int addLevel = GenerateAddOption(item.addOption);
		// Set additional option level
	}
	
	// Apply excellent options
	if (item.excellentOption > 0)
	{
		int excellentOptions = GenerateExcellentOption(item.excellentOption);
		// Set excellent options
	}
	
	// Apply time restrictions
	if (item.useTime > 0)
	{
		ApplyTimeRestriction(item);
	}
}

bool CustomMonsterDrop::GenerateLuckyAttribute(int rate)
{
	if (rate <= 0) return false;
	return GetRandomNumber(1, 100) <= rate;
}

bool CustomMonsterDrop::GenerateSkillAttribute(int rate)
{
	if (rate <= 0) return false;
	return GetRandomNumber(1, 100) <= rate;
}

int CustomMonsterDrop::GenerateAddOption(int addOptionType)
{
	switch (addOptionType)
	{
	case 0: return 0; // No additional option
	case 8: return 28; // Fixed +28
	default:
		if (addOptionType >= 1 && addOptionType <= 7)
		{
			return GetRandomNumber(0, addOptionType * 4); // Random 0~28 based on type
		}
		if (addOptionType >= 11 && addOptionType <= 17)
		{
			return 4 + (addOptionType - 11) * 4; // Fixed +4~+28
		}
		break;
	}
	return 0;
}

int CustomMonsterDrop::GenerateExcellentOption(int excellentConfig)
{
	if (excellentConfig <= 0) return 0;
	
	// This is a simplified implementation
	// In a real system, you would have more complex excellent option generation
	if (excellentConfig >= 1 && excellentConfig <= 6)
	{
		int optionCount = GetRandomNumber(1, excellentConfig);
		int options = 0;
		for (int i = 0; i < optionCount; i++)
		{
			int option = GetRandomNumber(0, 5);
			options |= (1 << option);
		}
		return options;
	}
	else if (excellentConfig == 7)
	{
		return 0x3F; // All excellent options
	}
	else if (excellentConfig >= 101 && excellentConfig <= 163)
	{
		return excellentConfig - 100; // Specific excellent option combination
	}
	
	return 0;
}

void CustomMonsterDrop::ApplyTimeRestriction(MonsterDropItem& item)
{
	// Calculate expiration time based on current time + useTime minutes
	// This would depend on your time system implementation
	// For example: item.expirationTime = currentTime + (item.useTime * 60);
}

// Configuration management functions
bool CustomMonsterDrop::AddMonsterConfig(int monsterId, const std::string& description)
{
	MonsterDropInfo info;
	info.monsterId = monsterId;
	info.description = description;
	m_monsterInfos[monsterId] = info;
	return true;
}

bool CustomMonsterDrop::UpdateBasicConfig(int monsterId, const MonsterDropBasic& config)
{
	m_basicConfigs[monsterId] = config;
	return true;
}

bool CustomMonsterDrop::AddLevelConfig(int monsterId, const MonsterDropLevel& config)
{
	m_levelConfigs[monsterId].push_back(config);
	return true;
}

bool CustomMonsterDrop::AddItemConfig(int monsterId, const MonsterDropItem& config)
{
	m_itemConfigs[monsterId].push_back(config);
	return true;
}

bool CustomMonsterDrop::RemoveMonsterConfig(int monsterId)
{
	m_monsterInfos.erase(monsterId);
	m_basicConfigs.erase(monsterId);
	m_levelConfigs.erase(monsterId);
	m_itemConfigs.erase(monsterId);
	return true;
}

// Getter functions
MonsterDropInfo* CustomMonsterDrop::GetMonsterInfo(int monsterId)
{
	auto it = m_monsterInfos.find(monsterId);
	return (it != m_monsterInfos.end()) ? &it->second : nullptr;
}

MonsterDropBasic* CustomMonsterDrop::GetBasicConfig(int monsterId)
{
	auto it = m_basicConfigs.find(monsterId);
	return (it != m_basicConfigs.end()) ? &it->second : nullptr;
}

std::vector<MonsterDropLevel>* CustomMonsterDrop::GetLevelConfigs(int monsterId)
{
	auto it = m_levelConfigs.find(monsterId);
	return (it != m_levelConfigs.end()) ? &it->second : nullptr;
}

std::vector<MonsterDropItem>* CustomMonsterDrop::GetItemConfigs(int monsterId)
{
	auto it = m_itemConfigs.find(monsterId);
	return (it != m_itemConfigs.end()) ? &it->second : nullptr;
}

// Utility functions
bool CustomMonsterDrop::IsMonsterConfigured(int monsterId)
{
	return m_monsterInfos.find(monsterId) != m_monsterInfos.end();
}

int CustomMonsterDrop::GetTotalWeight(const std::vector<MonsterDropItem>& items)
{
	int totalWeight = 0;
	for (const auto& item : items)
	{
		totalWeight += item.weight;
	}
	return totalWeight;
}

void CustomMonsterDrop::PrintDropStatistics(int monsterId)
{
	std::vector<MonsterDropItem>* items = GetItemConfigs(monsterId);
	if (!items)
	{
		LogError("No items configured for monster " + std::to_string(monsterId));
		return;
	}
	
	int totalWeight = GetTotalWeight(*items);
	LogInfo("Drop statistics for monster " + std::to_string(monsterId) + ":");
	LogInfo("Total items: " + std::to_string(items->size()));
	LogInfo("Total weight: " + std::to_string(totalWeight));
	
	for (const auto& item : *items)
	{
		double percentage = (double)item.weight / totalWeight * 100.0;
		LogInfo("Item [" + std::to_string(item.itemType1) + "," + std::to_string(item.itemType2) + 
			"] Weight: " + std::to_string(item.weight) + " (" + std::to_string(percentage) + "%)");
	}
}

// String utility functions
std::vector<std::string> CustomMonsterDrop::SplitString(const std::string& str, char delimiter)
{
	std::vector<std::string> tokens;
	std::stringstream ss(str);
	std::string token;
	
	while (std::getline(ss, token, delimiter))
	{
		tokens.push_back(TrimString(token));
	}
	
	return tokens;
}

std::string CustomMonsterDrop::TrimString(const std::string& str)
{
	size_t start = str.find_first_not_of(" \t\r\n");
	if (start == std::string::npos) return "";
	
	size_t end = str.find_last_not_of(" \t\r\n");
	return str.substr(start, end - start + 1);
}

bool CustomMonsterDrop::IsCommentLine(const std::string& line)
{
	return !line.empty() && line[0] == '/';
}

bool CustomMonsterDrop::IsEmptyLine(const std::string& line)
{
	return line.empty();
}

// Random number generation
int CustomMonsterDrop::GetRandomNumber(int min, int max)
{
	if (min > max) return min;
	std::uniform_int_distribution<int> dis(min, max);
	return dis(gen);
}

int CustomMonsterDrop::GetWeightedRandomIndex(const std::vector<int>& weights)
{
	int totalWeight = 0;
	for (int weight : weights)
	{
		totalWeight += weight;
	}
	
	if (totalWeight <= 0) return -1;
	
	int randomValue = GetRandomNumber(1, totalWeight);
	int currentWeight = 0;
	
	for (size_t i = 0; i < weights.size(); i++)
	{
		currentWeight += weights[i];
		if (randomValue <= currentWeight)
		{
			return static_cast<int>(i);
		}
	}
	
	return -1;
}

// Logging functions
void CustomMonsterDrop::LogError(const std::string& message)
{
	std::cerr << "[CustomMonsterDrop ERROR] " << message << std::endl;
}

void CustomMonsterDrop::LogInfo(const std::string& message)
{
	std::cout << "[CustomMonsterDrop INFO] " << message << std::endl;
}
