#pragma once

#include <vector>
#include <string>
#include <map>
#include <memory>

// Monster drop configuration data structures based on Monster_1000.txt format

// Section 0: Monster description
struct MonsterDropInfo
{
	int monsterId;			// Monster ID (e.g. 1000)
	std::string description;	// Monster description
	
	MonsterDropInfo() : monsterId(0) {}
};

// Section 1: Basic drop configuration  
struct MonsterDropBasic
{
	int mapNumber;			// Map where monster is located
	int dropEnabled;		// 1=enabled, 0=disabled
	int useGuaranteedDrop;		// 1=use guaranteed drop, 0=normal drop
	int guaranteedDropCount;	// Number of guaranteed drops per kill
	int levelRange;			// Item level range configuration
	int reserved1;			// Reserved parameter 1
	int reserved2;			// Reserved parameter 2  
	int reserved3;			// Reserved parameter 3
	
	MonsterDropBasic() : mapNumber(0), dropEnabled(0), useGuaranteedDrop(0), 
		guaranteedDropCount(0), levelRange(0), reserved1(0), reserved2(0), reserved3(0) {}
};

// Section 2: Level restriction configuration
struct MonsterDropLevel
{
	int useLevel;			// Level restriction (-1~19)
	int weight;			// Drop probability weight
	int additionalOption;		// Additional option configuration
	int excellentRate;		// Excellent option rate
	int excellentType;		// Single/double excellent type
	int setOption;			// Set equipment option
	int socketOption;		// Socket option
	int minLevel;			// Minimum level restriction
	int maxLevel;			// Maximum level restriction
	int reserved1;			// Reserved parameter 1
	int reserved2;			// Reserved parameter 2
	
	MonsterDropLevel() : useLevel(0), weight(1), additionalOption(0), excellentRate(0),
		excellentType(0), setOption(0), socketOption(0), minLevel(0), maxLevel(5001), 
		reserved1(0), reserved2(0) {}
};

// Section 3: Specific item drop configuration
struct MonsterDropItem
{
	int level;			// Item drop level (-1~19)
	int itemType1;			// Item category
	int itemType2;			// Item sub-category  
	int minLevel;			// Minimum item level (0~15)
	int maxLevel;			// Maximum item level (0~15)
	int luckyRate;			// Lucky attribute rate (0-100)
	int skillRate;			// Skill attribute rate (0-100)
	int addOption;			// Additional option (0=off, 1~7=random 0~28, 8=fixed 28)
	int durability;			// Durability (0~255)
	int excellentOption;		// Excellent option configuration
	int setItemValue;		// Set item value
	int socketCount;		// Socket count
	int useTime;			// Usage time limit (minutes)
	int accountRestriction;		// Account/VIP restriction
	int reserved;			// Reserved parameter
	int weight;			// Drop weight
	
	MonsterDropItem() : level(0), itemType1(0), itemType2(0), minLevel(0), maxLevel(0),
		luckyRate(0), skillRate(0), addOption(0), durability(0), excellentOption(0),
		setItemValue(0), socketCount(0), useTime(0), accountRestriction(0), 
		reserved(0), weight(1) {}
};

// Main custom monster drop management class
class CustomMonsterDrop
{
private:
	std::map<int, MonsterDropInfo> m_monsterInfos;		// Monster descriptions
	std::map<int, MonsterDropBasic> m_basicConfigs;	// Basic drop configs
	std::map<int, std::vector<MonsterDropLevel>> m_levelConfigs;	// Level restrictions
	std::map<int, std::vector<MonsterDropItem>> m_itemConfigs;	// Item configurations
	
	std::string m_configPath;				// Configuration file path
	bool m_initialized;					// Initialization status
	
public:
	CustomMonsterDrop();
	~CustomMonsterDrop();
	
	// Initialization and configuration loading
	bool Initialize(const std::string& configPath);
	void Clear();
	
	// Configuration file operations
	bool LoadMonsterDropConfig(int monsterId);
	bool LoadMonsterDropFile(const std::string& filename, int monsterId);
	bool SaveMonsterDropConfig(int monsterId);
	
	// Drop logic implementation
	std::vector<MonsterDropItem> GenerateDropItems(int monsterId, int playerLevel, 
		int playerVipLevel = 0, int playerAccountType = 0);
	MonsterDropItem* SelectRandomItem(const std::vector<MonsterDropItem>& items);
	bool CheckDropConditions(const MonsterDropItem& item, int playerLevel, 
		int playerVipLevel, int playerAccountType);
	
	// Item generation utilities
	void ApplyItemAttributes(MonsterDropItem& item);
	bool GenerateLuckyAttribute(int rate);
	bool GenerateSkillAttribute(int rate);  
	int GenerateAddOption(int addOptionType);
	int GenerateExcellentOption(int excellentConfig);
	void ApplyTimeRestriction(MonsterDropItem& item);
	
	// Configuration management
	bool AddMonsterConfig(int monsterId, const std::string& description);
	bool UpdateBasicConfig(int monsterId, const MonsterDropBasic& config);
	bool AddLevelConfig(int monsterId, const MonsterDropLevel& config);
	bool AddItemConfig(int monsterId, const MonsterDropItem& config);
	bool RemoveMonsterConfig(int monsterId);
	
	// Getters
	MonsterDropInfo* GetMonsterInfo(int monsterId);
	MonsterDropBasic* GetBasicConfig(int monsterId);
	std::vector<MonsterDropLevel>* GetLevelConfigs(int monsterId);
	std::vector<MonsterDropItem>* GetItemConfigs(int monsterId);
	
	// Utility functions
	bool IsMonsterConfigured(int monsterId);
	int GetTotalWeight(const std::vector<MonsterDropItem>& items);
	void PrintDropStatistics(int monsterId);
	
private:
	// File parsing helpers
	bool ParseSection0(const std::string& line, MonsterDropInfo& info);
	bool ParseSection1(const std::string& line, MonsterDropBasic& config);
	bool ParseSection2(const std::string& line, MonsterDropLevel& config);
	bool ParseSection3(const std::string& line, MonsterDropItem& config);
	
	// String processing utilities
	std::vector<std::string> SplitString(const std::string& str, char delimiter);
	std::string TrimString(const std::string& str);
	bool IsCommentLine(const std::string& line);
	bool IsEmptyLine(const std::string& line);
	
	// Random number generation
	int GetRandomNumber(int min, int max);
	int GetWeightedRandomIndex(const std::vector<int>& weights);
	
	// Logging and debugging
	void LogError(const std::string& message);
	void LogInfo(const std::string& message);
};

