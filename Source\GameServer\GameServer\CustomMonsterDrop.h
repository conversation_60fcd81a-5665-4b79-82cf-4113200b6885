//////////////////////////////////////////////////////////////////////
// CustomMonsterDrop.h - Custom Monster Drop System
// Based on Monster_1000.txt configuration file format
//////////////////////////////////////////////////////////////////////

#pragma once

#include "User.h"
#include <vector>
#include <unordered_map>
#include <string>

// Custom monster drop item structure based on Monster_1000.txt format
struct CUSTOM_DROP_ITEM
{
    int Index;              // Item index (物品索引)
    int Level;              // Item level (物品等级 0-15)
    int Grade;              // Item grade (物品品质 0-6)
    int Option0;            // Additional option (附加选项 0-15)
    int Option1;            // Lucky option (幸运选项 0-1)
    int Option2;            // Skill option (技能选项 0-1)
    int Option3;            // Extended option 1 (扩展选项1)
    int Option4;            // Extended option 2 (扩展选项2)
    int Option5;            // Extended option 3 (扩展选项3)
    int Option6;            // Extended option 4 (扩展选项4)
    int Duration;           // Item duration (持续时间 255=永久)
    int MapNumber;          // Map restriction (地图限制 -1=所有地图)
    int MonsterClass;       // Monster class (怪物类型)
    int MonsterLevelMin;    // Minimum monster level (怪物最低等级)
    int MonsterLevelMax;    // Maximum monster level (怪物最高等级)
    int DropRate;           // Drop rate (掉落概率 1-10000)

    CUSTOM_DROP_ITEM()
    {
        memset(this, 0, sizeof(CUSTOM_DROP_ITEM));
        Duration = 255;
        MapNumber = -1;
        DropRate = 1;
    }
};

// Monster drop group for optimized processing
struct CUSTOM_DROP_GROUP
{
    int MonsterClass;                           // Monster class ID
    std::vector<CUSTOM_DROP_ITEM> Items;        // Drop items list
    int TotalDropRate;                          // Total drop rate for quick calculation
    std::vector<int> CumulativeWeights;         // Cumulative weights for binary search
    bool WeightsCalculated;                     // Flag for weight calculation

    CUSTOM_DROP_GROUP()
    {
        MonsterClass = 0;
        TotalDropRate = 0;
        WeightsCalculated = false;
    }

    // Calculate cumulative weights for optimized random selection
    void CalculateCumulativeWeights()
    {
        CumulativeWeights.clear();
        CumulativeWeights.reserve(Items.size());

        int cumulative = 0;
        for (const auto& item : Items)
        {
            cumulative += item.DropRate;
            CumulativeWeights.push_back(cumulative);
        }

        TotalDropRate = cumulative;
        WeightsCalculated = true;
    }

    // Get random item index using binary search (O(log n))
    int GetRandomItemIndex() const
    {
        if (!WeightsCalculated || CumulativeWeights.empty())
        {
            return -1;
        }

        int randomValue = GetLargeRand() % TotalDropRate;

        // Binary search for the item
        int left = 0;
        int right = static_cast<int>(CumulativeWeights.size()) - 1;

        while (left < right)
        {
            int mid = left + (right - left) / 2;
            if (randomValue < CumulativeWeights[mid])
            {
                right = mid;
            }
            else
            {
                left = mid + 1;
            }
        }

        return left;
    }
};

// Main custom monster drop management class
class CustomMonsterDrop
{
private:
	std::map<int, MonsterDropInfo> m_monsterInfos;		// Monster descriptions
	std::map<int, MonsterDropBasic> m_basicConfigs;	// Basic drop configs
	std::map<int, std::vector<MonsterDropLevel>> m_levelConfigs;	// Level restrictions
	std::map<int, std::vector<MonsterDropItem>> m_itemConfigs;	// Item configurations
	
	std::string m_configPath;				// Configuration file path
	bool m_initialized;					// Initialization status
	
public:
	CustomMonsterDrop();
	~CustomMonsterDrop();
	
	// Initialization and configuration loading
	bool Initialize(const std::string& configPath);
	void Clear();
	
	// Configuration file operations
	bool LoadMonsterDropConfig(int monsterId);
	bool LoadMonsterDropFile(const std::string& filename, int monsterId);
	bool SaveMonsterDropConfig(int monsterId);
	
	// Drop logic implementation
	std::vector<MonsterDropItem> GenerateDropItems(int monsterId, int playerLevel, 
		int playerVipLevel = 0, int playerAccountType = 0);
	MonsterDropItem* SelectRandomItem(const std::vector<MonsterDropItem>& items);
	bool CheckDropConditions(const MonsterDropItem& item, int playerLevel, 
		int playerVipLevel, int playerAccountType);
	
	// Item generation utilities
	void ApplyItemAttributes(MonsterDropItem& item);
	bool GenerateLuckyAttribute(int rate);
	bool GenerateSkillAttribute(int rate);  
	int GenerateAddOption(int addOptionType);
	int GenerateExcellentOption(int excellentConfig);
	void ApplyTimeRestriction(MonsterDropItem& item);
	
	// Configuration management
	bool AddMonsterConfig(int monsterId, const std::string& description);
	bool UpdateBasicConfig(int monsterId, const MonsterDropBasic& config);
	bool AddLevelConfig(int monsterId, const MonsterDropLevel& config);
	bool AddItemConfig(int monsterId, const MonsterDropItem& config);
	bool RemoveMonsterConfig(int monsterId);
	
	// Getters
	MonsterDropInfo* GetMonsterInfo(int monsterId);
	MonsterDropBasic* GetBasicConfig(int monsterId);
	std::vector<MonsterDropLevel>* GetLevelConfigs(int monsterId);
	std::vector<MonsterDropItem>* GetItemConfigs(int monsterId);
	
	// Utility functions
	bool IsMonsterConfigured(int monsterId);
	int GetTotalWeight(const std::vector<MonsterDropItem>& items);
	void PrintDropStatistics(int monsterId);
	
private:
	// File parsing helpers
	bool ParseSection0(const std::string& line, MonsterDropInfo& info);
	bool ParseSection1(const std::string& line, MonsterDropBasic& config);
	bool ParseSection2(const std::string& line, MonsterDropLevel& config);
	bool ParseSection3(const std::string& line, MonsterDropItem& config);
	
	// String processing utilities
	std::vector<std::string> SplitString(const std::string& str, char delimiter);
	std::string TrimString(const std::string& str);
	bool IsCommentLine(const std::string& line);
	bool IsEmptyLine(const std::string& line);
	
	// Random number generation
	int GetRandomNumber(int min, int max);
	int GetWeightedRandomIndex(const std::vector<int>& weights);
	
	// Logging and debugging
	void LogError(const std::string& message);
	void LogInfo(const std::string& message);
};
